import { CommonModule } from '@angular/common';
import { Component, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { ButtonComponent, AvaTextareaComponent } from '@ava/play-comp-library';
import { CalendarComponent } from '@ava/play-comp-library';
import { LinkComponent } from '@ava/play-comp-library';
import { SliderComponent } from '@ava/play-comp-library';
import { TextCardComponent } from '@ava/play-comp-library';
import { AvaTextboxComponent } from '@ava/play-comp-library';
import { DropdownComponent } from '@ava/play-comp-library';
import { HttpService } from './service/http.service';

@Component({
  selector: 'app-root',
  imports: [CommonModule,
    ButtonComponent,
    CalendarComponent,
    LinkComponent,
    SliderComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent, AvaTextareaComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  title = 'play-check';
  sliderValue = signal(50);
  active = true;

  categoryOptions = signal<any[]>([]);
  categoryOptions1: any[] = [];
  categoryDisabled = false;
  constructor(private httpService: HttpService) {

  }
  onValueChange(event: any) {
    console.log(event);
  }

  dark() {
    document.documentElement.classList.remove('light-theme');
    document.documentElement.classList.add('dark-theme');
    this.active = false;
  }
  light() {
    document.documentElement.classList.remove('dark-theme');
    document.documentElement.classList.add('light-theme');
    this.active = true;
  }

  onDateSelected(event: any) {
    console.log('event calendar', event);
  }
  /// Dropdown
  ngOnInit(): void {
    this.categoryDisabled = false;
    this.httpService.getPosts().subscribe((data: any) => {
      console.log('data', data);
      const cat: any[] = [];
      data.forEach((element: any) => {
        let c = {
          name: element.userId,
          value: element.userId,
        }
        cat.push(c);
      });
      console.log(cat);
      this.categoryOptions.set(cat);
      this.categoryDisabled = true;
      this.categoryOptions1 = cat;
    });
  }

  onCategoryChange(event: any) {

  }

  ////


}
