import { CommonModule } from '@angular/common';
import { Component, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { ButtonComponent, AvaTextareaComponent } from '@ava/play-comp-library';
import { CalendarComponent } from '@ava/play-comp-library';
import { LinkComponent } from '@ava/play-comp-library';
import { SliderComponent } from '@ava/play-comp-library';
import { TextCardComponent } from '@ava/play-comp-library';
import { AvaTextboxComponent } from '@ava/play-comp-library';
import { DropdownComponent } from '@ava/play-comp-library';
import { HttpService } from './service/http.service';
import { CheckboxService } from './service/checkbox.service';
import { CheckboxOption, CheckboxChangeEvent } from './models/checkbox.model';
import { CheckboxComponent } from './components/checkbox.component';

@Component({
  selector: 'app-root',
  imports: [CommonModule,
    ButtonComponent,
    CalendarComponent,
    LinkComponent,
    SliderComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    AvaTextareaComponent,
    CheckboxComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  title = 'play-check';
  sliderValue = signal(50);
  active = true;

  // Dropdown properties
  categoryOptions = signal<any[]>([]);
  categoryOptions1: any[] = [];
  categoryDisabled = false;

  // Checkbox properties using signals (Angular 19 style)
  checkboxOptions = signal<CheckboxOption[]>([]);
  checkboxLoading = signal<boolean>(false);
  checkboxError = signal<string | null>(null);

  constructor(
    private httpService: HttpService,
    private checkboxService: CheckboxService
  ) {}
  onValueChange(event: any) {
    console.log(event);
  }

  dark() {
    document.documentElement.classList.remove('light-theme');
    document.documentElement.classList.add('dark-theme');
    this.active = false;
  }
  light() {
    document.documentElement.classList.remove('dark-theme');
    document.documentElement.classList.add('light-theme');
    this.active = true;
  }

  onDateSelected(event: any) {
    console.log('event calendar', event);
  }
  /// Dropdown and Checkbox initialization
  ngOnInit(): void {
    // Initialize dropdown
    this.categoryDisabled = false;
    this.httpService.getPosts().subscribe((data: any) => {
      console.log('data', data);
      const cat: any[] = [];
      data.forEach((element: any) => {
        let c = {
          name: element.userId,
          value: element.userId,
        }
        cat.push(c);
      });
      console.log(cat);
      this.categoryOptions.set(cat);
      this.categoryDisabled = true;
      this.categoryOptions1 = cat;
    });

    // Initialize checkbox options using the service
    this.loadCheckboxOptions();
  }

  onCategoryChange(event: any) {
    console.log('Category changed:', event);
  }

  /// Checkbox methods
  loadCheckboxOptions(): void {
    this.checkboxLoading.set(true);
    this.checkboxService.loadCheckboxOptions().subscribe({
      next: (options) => {
        this.checkboxOptions.set(options);
        this.checkboxLoading.set(false);
        console.log('Checkbox options loaded:', options);
      },
      error: (error) => {
        this.checkboxError.set(error.message || 'Failed to load checkbox options');
        this.checkboxLoading.set(false);
        console.error('Error loading checkbox options:', error);
      }
    });
  }

  onCheckboxChange(option: CheckboxOption, isChecked: boolean): void {
    console.log('Checkbox changed:', option.label, 'checked:', isChecked);
    this.checkboxService.onCheckboxChange(option, isChecked);
  }

  // Utility methods for checkbox management
  resetAllCheckboxes(): void {
    this.checkboxService.resetAllCheckboxes();
  }

  checkAllCheckboxes(): void {
    this.checkboxService.checkAllCheckboxes();
  }

  getCheckedOptions(): CheckboxOption[] {
    return this.checkboxService.getCheckedOptions();
  }

  ////


}
