import { CommonModule } from '@angular/common';
import { Component, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { ButtonComponent, AvaTextareaComponent, CheckboxComponent } from '@ava/play-comp-library';
import { CalendarComponent } from '@ava/play-comp-library';
import { LinkComponent } from '@ava/play-comp-library';
import { SliderComponent } from '@ava/play-comp-library';
import { TextCardComponent } from '@ava/play-comp-library';
import { AvaTextboxComponent } from '@ava/play-comp-library';
import { DropdownComponent } from '@ava/play-comp-library';
import { HttpService } from './service/http.service';

@Component({
  selector: 'app-root',
  imports: [CommonModule,
    ButtonComponent,
    CalendarComponent,
    LinkComponent,
    SliderComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    AvaTextareaComponent,
    CheckboxComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  title = 'play-check';
  sliderValue = signal(50);
  active = true;

  // Dropdown properties
  categoryOptions = signal<any[]>([]);
  categoryOptions1: any[] = [];
  categoryDisabled = false;

  // Checkbox properties - simple like dropdown
  checkboxOptions1: any[] = [];

  // Demo states for original checkbox examples
  demoStates = {
    basicDefault: false,
    basicWithBg: false,
    basicAnimated: false
  };

  constructor(private httpService: HttpService) {}
  onValueChange(event: any) {
    console.log(event);
  }

  dark() {
    document.documentElement.classList.remove('light-theme');
    document.documentElement.classList.add('dark-theme');
    this.active = false;
  }
  light() {
    document.documentElement.classList.remove('dark-theme');
    document.documentElement.classList.add('light-theme');
    this.active = true;
  }

  onDateSelected(event: any) {
    console.log('event calendar', event);
  }
  /// Dropdown and Checkbox initialization
  ngOnInit(): void {
    // Initialize dropdown
    this.categoryDisabled = false;
    this.httpService.getPosts().subscribe((data: any) => {
      console.log('data', data);
      const cat: any[] = [];
      data.forEach((element: any) => {
        let c = {
          name: element.userId,
          value: element.userId,
        }
        cat.push(c);
      });
      console.log(cat);
      this.categoryOptions.set(cat);
      this.categoryDisabled = true;
      this.categoryOptions1 = cat;
    });

    // Initialize checkbox options - simple like dropdown
    this.httpService.getCheckboxOptions().subscribe((data: any) => {
      console.log('checkbox data', data);
      if (data.success) {
        this.checkboxOptions1 = data.data;
      }
    });
  }

  onCategoryChange(event: any) {
    console.log('Category changed:', event);
  }

  /// Checkbox methods - simple like dropdown
  onCheckboxChange(option: any, isChecked: boolean): void {
    console.log('Checkbox changed:', option.label, 'checked:', isChecked);
    // Update the option in the array
    const updatedOptions = this.checkboxOptions1.map((opt: any) =>
      opt.id === option.id
        ? { ...opt, isChecked }
        : opt
    );
    this.checkboxOptions1 = updatedOptions;
  }

  // Simple utility methods
  getCheckedOptions(): any[] {
    return this.checkboxOptions1.filter((option: any) => option.isChecked);
  }

  ////


}
