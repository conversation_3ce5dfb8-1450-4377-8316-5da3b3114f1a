import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AngularAppEngine,
  InlineCriticalCssProcessor,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig,
  provideServerRoutesConfig,
  provideServerRouting,
  setAngularAppEngineManifest,
  setAngularAppManifest,
  withAppShell
} from "./chunk-DWYSVEUR.js";
import "./chunk-CC4F4OUU.js";
import "./chunk-FYXEBGAD.js";
import "./chunk-SY4YM4RU.js";
import "./chunk-XNYIGXTF.js";
import "./chunk-53KWUNRH.js";
import "./chunk-DFIAP3GC.js";
import "./chunk-PXXRCHXC.js";
import "./chunk-YHCV7DAQ.js";
export {
  AngularAppEng<PERSON>,
  PrerenderFallback,
  RenderMode,
  createRequestHand<PERSON>,
  provideServerRoutesConfig,
  provideServerRouting,
  withAppShell,
  InlineCriticalCssProcessor as ɵInlineCriticalCssProcessor,
  destroyAngularServerApp as ɵdestroyAngularServerApp,
  extractRoutesAndCreateRouteTree as ɵextractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as ɵgetOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig as ɵgetRoutesFromAngularRouterConfig,
  setAngularAppEngineManifest as ɵsetAngularAppEngineManifest,
  setAngularAppManifest as ɵsetAngularAppManifest
};
