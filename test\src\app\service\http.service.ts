import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { CheckboxOption, CheckboxApiResponse } from '../models/checkbox.model';

@Injectable({
  providedIn: 'root'
})
export class HttpService {

  private http = inject(HttpClient);

  getPosts() {
    return this.http.get('https://jsonplaceholder.typicode.com/posts');
  }

  // Checkbox API methods
  getCheckboxOptions(): Observable<CheckboxApiResponse> {
    // Simulating API call with mock data
    const mockCheckboxOptions: CheckboxOption[] = [
      {
        id: '1',
        label: 'Default Option',
        value: 'default-1',
        variant: 'default',
        isChecked: false,
        indeterminate: false
      },
      {
        id: '2',
        label: 'With Background Option',
        value: 'with-bg-1',
        variant: 'with-bg',
        isChecked: true,
        indeterminate: false
      },
      {
        id: '3',
        label: 'Animated Option',
        value: 'animated-1',
        variant: 'animated',
        isChecked: false,
        indeterminate: false
      },
      {
        id: '4',
        label: 'Indeterminate Option',
        value: 'default-2',
        variant: 'default',
        isChecked: false,
        indeterminate: true
      }
    ];

    const response: CheckboxApiResponse = {
      success: true,
      data: mockCheckboxOptions,
      message: 'Checkbox options retrieved successfully'
    };

    return of(response);
  }

  updateCheckboxState(checkboxId: string, isChecked: boolean): Observable<CheckboxApiResponse> {
    // Simulating API call for updating checkbox state
    const response: CheckboxApiResponse = {
      success: true,
      data: [],
      message: `Checkbox ${checkboxId} updated to ${isChecked ? 'checked' : 'unchecked'}`
    };

    return of(response);
  }
}
