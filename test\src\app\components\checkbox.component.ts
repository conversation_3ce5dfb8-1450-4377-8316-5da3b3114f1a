import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CheckboxVariant } from '../models/checkbox.model';

@Component({
  selector: 'ava-checkbox',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="checkbox-wrapper" [class]="'checkbox-' + variant">
      <label class="checkbox-label">
        <input 
          type="checkbox" 
          class="checkbox-input"
          [checked]="isChecked"
          [indeterminate]="indeterminate"
          (change)="onCheckboxChange($event)"
          [disabled]="disabled">
        <span class="checkbox-custom" [class.indeterminate]="indeterminate"></span>
        <span class="checkbox-text" *ngIf="label">{{ label }}</span>
      </label>
    </div>
  `,
  styles: [`
    .checkbox-wrapper {
      display: inline-flex;
      align-items: center;
      margin: 4px 0;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      cursor: pointer;
      user-select: none;
    }

    .checkbox-input {
      position: absolute;
      opacity: 0;
      width: 0;
      height: 0;
    }

    .checkbox-custom {
      width: 18px;
      height: 18px;
      border: 2px solid #ccc;
      border-radius: 3px;
      margin-right: 8px;
      position: relative;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .checkbox-custom::after {
      content: '';
      width: 5px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .checkbox-input:checked + .checkbox-custom {
      background-color: #007bff;
      border-color: #007bff;
    }

    .checkbox-input:checked + .checkbox-custom::after {
      opacity: 1;
    }

    .checkbox-custom.indeterminate {
      background-color: #007bff;
      border-color: #007bff;
    }

    .checkbox-custom.indeterminate::after {
      content: '';
      width: 8px;
      height: 2px;
      background-color: white;
      border: none;
      transform: none;
      opacity: 1;
    }

    .checkbox-text {
      font-size: 14px;
      color: #333;
    }

    .checkbox-input:disabled + .checkbox-custom {
      background-color: #f5f5f5;
      border-color: #ddd;
      cursor: not-allowed;
    }

    .checkbox-input:disabled ~ .checkbox-text {
      color: #999;
      cursor: not-allowed;
    }

    /* Variant styles */
    .checkbox-default .checkbox-custom {
      border-radius: 3px;
    }

    .checkbox-with-bg .checkbox-custom {
      background-color: #f8f9fa;
      border-color: #6c757d;
    }

    .checkbox-with-bg .checkbox-input:checked + .checkbox-custom {
      background-color: #28a745;
      border-color: #28a745;
    }

    .checkbox-animated .checkbox-custom {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      transform: scale(1);
    }

    .checkbox-animated .checkbox-input:checked + .checkbox-custom {
      transform: scale(1.1);
      background-color: #17a2b8;
      border-color: #17a2b8;
    }

    .checkbox-animated .checkbox-custom::after {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  `]
})
export class CheckboxComponent {
  @Input() variant: CheckboxVariant = 'default';
  @Input() label: string = '';
  @Input() isChecked: boolean = false;
  @Input() indeterminate: boolean = false;
  @Input() disabled: boolean = false;

  @Output() isCheckedChange = new EventEmitter<boolean>();

  onCheckboxChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.isCheckedChange.emit(target.checked);
  }
}
