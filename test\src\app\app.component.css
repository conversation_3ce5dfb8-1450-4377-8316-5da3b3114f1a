.slider{
  margin-top: 2rem;
  width: 400px;
  margin:50px;
}

.checkbox-api-demo {
  max-width: 800px;
  margin: 0 auto;
}

.checkbox-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-controls button {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.checkbox-controls button:hover {
  opacity: 0.9;
}

.selected-options {
  border-left: 4px solid #007bff;
}

.demo-header h2 {
  color: #007bff;
  margin-bottom: 8px;
}

.demo-header p {
  color: #6c757d;
  margin-bottom: 20px;
}