import { Injectable, signal } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { HttpService } from './http.service';

@Injectable({
  providedIn: 'root'
})
export class CheckboxService {

  // Angular 19 signals for reactive state management
  private _checkboxOptions = signal<any[]>([]);
  private _checkboxStates = signal<any>({});
  private _loading = signal<boolean>(false);
  private _error = signal<string | null>(null);

  // BehaviorSubject for checkbox change events
  private _checkboxChanges = new BehaviorSubject<any>(null);

  constructor(private httpService: HttpService) {}

  // Public readonly signals
  get checkboxOptions() {
    return this._checkboxOptions.asReadonly();
  }

  get checkboxStates() {
    return this._checkboxStates.asReadonly();
  }

  get loading() {
    return this._loading.asReadonly();
  }

  get error() {
    return this._error.asReadonly();
  }

  get checkboxChanges$(): Observable<any> {
    return this._checkboxChanges.asObservable();
  }

  /**
   * Load checkbox options from API
   * Similar to how dropdown loads options
   */
  loadCheckboxOptions(): Observable<any[]> {
    this._loading.set(true);
    this._error.set(null);

    return this.httpService.getCheckboxOptions().pipe(
      map((response: any) => {
        if (response.success) {
          return response.data;
        } else {
          throw new Error(response.message || 'Failed to load checkbox options');
        }
      }),
      tap((options: any[]) => {
        this._checkboxOptions.set(options);

        // Initialize checkbox states
        const initialStates: any = {};
        options.forEach(option => {
          initialStates[option.id] = option.isChecked;
        });
        this._checkboxStates.set(initialStates);

        this._loading.set(false);
      }),
      tap({
        error: (error) => {
          this._error.set(error.message || 'Unknown error occurred');
          this._loading.set(false);
        }
      })
    );
  }

  /**
   * Handle checkbox change event
   * Similar to dropdown's selectionChange
   */
  onCheckboxChange(option: any, isChecked: boolean): void {
    // Update local state
    const currentStates = this._checkboxStates();
    const updatedStates = { ...currentStates, [option.id]: isChecked };
    this._checkboxStates.set(updatedStates);

    // Update the option in the options array
    const currentOptions = this._checkboxOptions();
    const updatedOptions = currentOptions.map(opt =>
      opt.id === option.id
        ? { ...opt, isChecked, indeterminate: false }
        : opt
    );
    this._checkboxOptions.set(updatedOptions);

    // Create change event
    const changeEvent = {
      option: { ...option, isChecked },
      isChecked,
      timestamp: new Date()
    };

    // Emit change event
    this._checkboxChanges.next(changeEvent);

    // Optionally update server state
    this.updateServerState(option.id, isChecked);
  }

  /**
   * Update checkbox state on server
   */
  private updateServerState(checkboxId: string, isChecked: boolean): void {
    this.httpService.updateCheckboxState(checkboxId, isChecked).subscribe({
      next: (response) => {
        if (!response.success) {
          console.error('Failed to update checkbox state on server:', response.message);
        }
      },
      error: (error) => {
        console.error('Error updating checkbox state:', error);
      }
    });
  }

  /**
   * Get checkbox option by ID
   */
  getCheckboxById(id: string): any | undefined {
    return this._checkboxOptions().find(option => option.id === id);
  }

  /**
   * Get all checked options
   */
  getCheckedOptions(): any[] {
    return this._checkboxOptions().filter(option => option.isChecked);
  }

  /**
   * Get checkbox state by ID
   */
  getCheckboxState(id: string): boolean {
    return this._checkboxStates()[id] || false;
  }

  /**
   * Set multiple checkbox states at once
   */
  setCheckboxStates(states: any): void {
    this._checkboxStates.set({ ...this._checkboxStates(), ...states });

    // Update options array to reflect new states
    const currentOptions = this._checkboxOptions();
    const updatedOptions = currentOptions.map(option => ({
      ...option,
      isChecked: states[option.id] !== undefined ? states[option.id] : option.isChecked
    }));
    this._checkboxOptions.set(updatedOptions);
  }

  /**
   * Reset all checkboxes to unchecked state
   */
  resetAllCheckboxes(): void {
    const currentOptions = this._checkboxOptions();
    const resetStates: any = {};

    const resetOptions = currentOptions.map(option => {
      resetStates[option.id] = false;
      return { ...option, isChecked: false, indeterminate: false };
    });

    this._checkboxOptions.set(resetOptions);
    this._checkboxStates.set(resetStates);
  }

  /**
   * Check all checkboxes
   */
  checkAllCheckboxes(): void {
    const currentOptions = this._checkboxOptions();
    const checkedStates: any = {};

    const checkedOptions = currentOptions.map(option => {
      checkedStates[option.id] = true;
      return { ...option, isChecked: true, indeterminate: false };
    });

    this._checkboxOptions.set(checkedOptions);
    this._checkboxStates.set(checkedStates);
  }
}
