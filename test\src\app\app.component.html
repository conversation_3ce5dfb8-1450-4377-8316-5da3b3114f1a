<div style="padding:50px;border:1px solid #ccc;width:500px;margin: 0 auto;margin-top: 100px;">

    <div style="width:400px;margin-top: 30px;">
        <ava-button label="Primary" variant="primary" width="160px"></ava-button>
    </div>

    <div style="width:400px;margin-top: 30px;">
        <ava-calendar (dateSelected)="onDateSelected($event)"></ava-calendar>
    </div>

    <div style="width:400px;margin-top: 30px;">
        <ava-link label="Underlined Link" color="success" [underline]="true"></ava-link>
    </div>

    <div style="width:400px;margin-top: 30px;">
        <ava-slider [value]=".3" [min]="0" [max]="100"></ava-slider>
    </div>


    <div style="width:400px;margin-top: 30px;">
        <ava-textarea label="Basic Textarea" placeholder="Type here..."></ava-textarea>
    </div>

    <div style="width:400px;margin-top: 30px;">
        <ava-textbox label="Email" type="email" placeholder="<EMAIL>">
        </ava-textbox>
    </div>
    <div style="width:400px;margin-top: 30px;">
        <ava-text-card [title]="'Create Tool'" [iconName]="'plus'" [type]="'create'" iconColor="#144692">
        </ava-text-card>
    </div>


</div>



<div class="cascading-dropdown-demo">

    <div class="demo-header">

        <h2>Cascading Dropdown Example</h2>

        <p>Select a category to enable the second dropdown with filtered options.</p>

    </div>



    <div class="dropdown-container">
        <!-- First Dropdown - Category Selection -->
        <div class="dropdown-section">
            <h4>Step 1: Select Category</h4>

            <ava-dropdown [disabled]="categoryOptions1.length <1" dropdownTitle="Select Category"
                [options]="categoryOptions1" [search]="true" (selectionChange)="onCategoryChange($event)">

            </ava-dropdown>{{categoryOptions().length}}

        </div>



        <!-- Second Dropdown - Sub-category (Disabled until first selection) -->

        <!-- <div class="dropdown-section">

            <h4>Step 2: Select Item</h4>

            <ng-container *ngIf="!isSecondDropdownDisabled; else disabledDropdown">

                <ava-dropdown [dropdownTitle]="getSecondDropdownTitle()" [options]="subCategoryOptions"
                    [disabled]="false" [search]="true" [selectedValue]="''"
                    (selectionChange)="onSubCategoryChange($event)">

                </ava-dropdown>

            </ng-container>



            <ng-template #disabledDropdown>

                <ava-dropdown [dropdownTitle]="getSecondDropdownTitle()" [options]="[]" [disabled]="true"
                    [search]="true">

                </ava-dropdown>

            </ng-template>

        </div>

    </div> -->



    <!-- Display Current Selections -->

    <!-- <div class="selections-display" *ngIf="selectedCategory || selectedSubCategory">

        <h4>Current Selections:</h4>

        <div class="selection-item" *ngIf="selectedCategory">

            <strong>Category:</strong> {{ selectedCategory }} ({{ selectedCategoryValue }})

        </div>

        <div class="selection-item" *ngIf="selectedSubCategory">

            <strong>{{ selectedCategory }}:</strong> {{ selectedSubCategory }} ({{ selectedSubCategoryValue }})

        </div> -->

    </div>

    <!-- Simple Checkbox Example - Same Pattern as Dropdown -->
    <div class="checkbox-demo" style="margin-top: 50px;">
        <div class="demo-header">
            <h2>Simple Checkbox Example</h2>
            <p>Checkboxes loaded from API - Same pattern as dropdown above</p>
        </div>

        <div class="checkbox-container">
            <!-- Checkbox Section - Simple like dropdown -->
            <div class="checkbox-section">
                <h4>Step 1: Select Options</h4>

                <div *ngFor="let option of checkboxOptions1" style="margin: 10px 0;">
                    <ava-checkbox
                        [variant]="option.variant"
                        [label]="option.label"
                        [isChecked]="option.isChecked"
                        [indeterminate]="option.indeterminate"
                        (isCheckedChange)="onCheckboxChange(option, $event)">
                    </ava-checkbox>
                </div>
                <p>Total options: {{checkboxOptions1.length}}</p>
            </div>
        </div>
    </div>

    <!-- Display Selected Options -->
    <div class="selected-options" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 4px;">
        <h5>Selected Options:</h5>
        <div *ngIf="getCheckedOptions().length === 0">
            <p style="color: #6c757d;">No options selected</p>
        </div>
        <div *ngFor="let selected of getCheckedOptions()" style="margin: 5px 0;">
            <strong>{{ selected.label }}</strong> ({{ selected.value }}) - {{ selected.variant }}
        </div>
    </div>

        <!-- Original Checkbox Examples -->
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 4px;">
            <h4>Your Original Checkbox Examples:</h4>
            <div style="margin: 10px 0;">
                <!-- Default Variant -->
                <ava-checkbox variant="default" label="Default" [isChecked]="demoStates.basicDefault"
                  (isCheckedChange)="demoStates.basicDefault = $event"></ava-checkbox>
            </div>
            <div style="margin: 10px 0;">
                <!-- With Background Variant -->
                <ava-checkbox variant="with-bg" label="With Background" [isChecked]="demoStates.basicWithBg"
                  (isCheckedChange)="demoStates.basicWithBg = $event"></ava-checkbox>
            </div>
            <div style="margin: 10px 0;">
                <!-- Animated Variant -->
                <ava-checkbox variant="animated" label="Animated" [isChecked]="demoStates.basicAnimated"
                  (isCheckedChange)="demoStates.basicAnimated = $event"></ava-checkbox>
            </div>
            <div style="margin: 10px 0;">
                <!-- Variants with Indeterminate State -->
                <ava-checkbox variant="default" label="Default Indeterminate" [indeterminate]="true"></ava-checkbox>
            </div>
            <div style="margin: 10px 0;">
                <ava-checkbox variant="with-bg" label="With-bg Indeterminate" [indeterminate]="true"></ava-checkbox>
            </div>
            <div style="margin: 10px 0;">
                <ava-checkbox variant="animated" label="Animated Indeterminate" [indeterminate]="true"></ava-checkbox>
            </div>
        </div>
    </div>