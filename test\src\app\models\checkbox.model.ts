/**
 * Angular 19 Checkbox Models with strict typing
 */

export type CheckboxVariant = 'default' | 'with-bg' | 'animated';

export interface CheckboxOption {
  readonly id: string;
  readonly label: string;
  readonly value: string;
  readonly variant: CheckboxVariant;
  readonly isChecked: boolean;
  readonly indeterminate: boolean;
}

export interface CheckboxChangeEvent {
  readonly option: CheckboxOption;
  readonly isChecked: boolean;
  readonly timestamp: Date;
}

export interface CheckboxGroupState {
  [key: string]: boolean;
}

export interface CheckboxApiResponse {
  readonly success: boolean;
  readonly data: CheckboxOption[];
  readonly message?: string;
}

export interface CheckboxStateUpdate {
  readonly checkboxId: string;
  readonly isChecked: boolean;
  readonly updatedAt: Date;
}
